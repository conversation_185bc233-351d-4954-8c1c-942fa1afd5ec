<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rust HTTP Server</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #ffd700;
        }
        .api-demo {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: #ffd700;
            color: #333;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        button:hover {
            background: #ffed4e;
        }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            background: #4ade80;
            color: #166534;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦀 Rust HTTP Server <span class="status">Running</span></h1>
        
        <div class="feature">
            <h3>🚀 High Performance</h3>
            <p>Built with Tokio and Hyper for maximum async performance and low resource usage.</p>
        </div>

        <div class="feature">
            <h3>📁 Static File Serving</h3>
            <p>Efficient static file serving with MIME type detection, caching headers, and directory listings.</p>
        </div>

        <div class="feature">
            <h3>🔌 RESTful API</h3>
            <p>JSON API endpoints with full CRUD operations, error handling, and proper HTTP status codes.</p>
        </div>

        <div class="feature">
            <h3>🛡️ Security & Middleware</h3>
            <p>CORS support, rate limiting, security headers, and comprehensive request/response logging.</p>
        </div>

        <div class="api-demo">
            <h3>🧪 API Demo</h3>
            <p>Try out the API endpoints:</p>
            
            <button onclick="testHealth()">Health Check</button>
            <button onclick="testStatus()">API Status</button>
            <button onclick="testCreateItem()">Create Item</button>
            <button onclick="testListItems()">List Items</button>
            
            <pre id="api-output">Click a button to test the API...</pre>
        </div>

        <div class="feature">
            <h3>📖 Available Endpoints</h3>
            <ul>
                <li><strong>GET /health</strong> - Health check</li>
                <li><strong>GET /api/status</strong> - API status</li>
                <li><strong>GET /api/items</strong> - List all items</li>
                <li><strong>POST /api/items</strong> - Create new item</li>
                <li><strong>GET /api/items/{id}</strong> - Get specific item</li>
                <li><strong>PUT /api/items/{id}</strong> - Update item</li>
                <li><strong>DELETE /api/items/{id}</strong> - Delete item</li>
            </ul>
        </div>
    </div>

    <script>
        const output = document.getElementById('api-output');

        async function makeRequest(method, url, body = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(url, options);
                const data = await response.text();
                
                output.textContent = `${method} ${url}\nStatus: ${response.status}\nResponse:\n${data}`;
            } catch (error) {
                output.textContent = `Error: ${error.message}`;
            }
        }

        function testHealth() {
            makeRequest('GET', '/health');
        }

        function testStatus() {
            makeRequest('GET', '/api/status');
        }

        function testCreateItem() {
            const item = {
                name: `Test Item ${Date.now()}`,
                description: 'Created from the web interface'
            };
            makeRequest('POST', '/api/items', item);
        }

        function testListItems() {
            makeRequest('GET', '/api/items');
        }
    </script>
</body>
</html>
