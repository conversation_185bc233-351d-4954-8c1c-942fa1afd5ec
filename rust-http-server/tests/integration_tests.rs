use std::time::Duration;
use reqwest;
use serde_json::{json, Value};
use tokio::time::sleep;

const BASE_URL: &str = "http://127.0.0.1:8080";

#[tokio::test]
async fn test_health_endpoint() {
    let client = reqwest::Client::new();
    let response = client.get(&format!("{}/health", BASE_URL))
        .send()
        .await
        .expect("Failed to send request");

    assert_eq!(response.status(), 200);
    
    let body: Value = response.json().await.expect("Failed to parse JSON");
    assert_eq!(body["status"], "healthy");
    assert!(body["timestamp"].is_string());
}

#[tokio::test]
async fn test_api_status_endpoint() {
    let client = reqwest::Client::new();
    let response = client.get(&format!("{}/api/status", BASE_URL))
        .send()
        .await
        .expect("Failed to send request");

    assert_eq!(response.status(), 200);
    
    let body: Value = response.json().await.expect("Failed to parse JSON");
    assert_eq!(body["status"], "ok");
    assert!(body["items_count"].is_number());
}

#[tokio::test]
async fn test_static_file_serving() {
    let client = reqwest::Client::new();
    let response = client.get(&format!("{}/", BASE_URL))
        .send()
        .await
        .expect("Failed to send request");

    assert_eq!(response.status(), 200);
    assert!(response.headers().get("content-type").unwrap().to_str().unwrap().starts_with("text/html"));
    
    let body = response.text().await.expect("Failed to get response body");
    assert!(body.contains("Rust HTTP Server"));
}

#[tokio::test]
async fn test_crud_operations() {
    let client = reqwest::Client::new();

    // Create an item
    let create_payload = json!({
        "name": "Test Item",
        "description": "A test item for integration testing"
    });

    let create_response = client.post(&format!("{}/api/items", BASE_URL))
        .json(&create_payload)
        .send()
        .await
        .expect("Failed to create item");

    assert_eq!(create_response.status(), 201);
    
    let created_item: Value = create_response.json().await.expect("Failed to parse created item");
    let item_id = created_item["id"].as_str().expect("Item should have an ID");
    assert_eq!(created_item["name"], "Test Item");

    // Get the item
    let get_response = client.get(&format!("{}/api/items/{}", BASE_URL, item_id))
        .send()
        .await
        .expect("Failed to get item");

    assert_eq!(get_response.status(), 200);
    let retrieved_item: Value = get_response.json().await.expect("Failed to parse retrieved item");
    assert_eq!(retrieved_item["id"], item_id);
    assert_eq!(retrieved_item["name"], "Test Item");

    // Update the item
    let update_payload = json!({
        "name": "Updated Test Item",
        "description": "Updated description"
    });

    let update_response = client.put(&format!("{}/api/items/{}", BASE_URL, item_id))
        .json(&update_payload)
        .send()
        .await
        .expect("Failed to update item");

    assert_eq!(update_response.status(), 200);
    let updated_item: Value = update_response.json().await.expect("Failed to parse updated item");
    assert_eq!(updated_item["name"], "Updated Test Item");

    // List items
    let list_response = client.get(&format!("{}/api/items", BASE_URL))
        .send()
        .await
        .expect("Failed to list items");

    assert_eq!(list_response.status(), 200);
    let list_result: Value = list_response.json().await.expect("Failed to parse items list");
    assert!(list_result["count"].as_u64().unwrap() >= 1);

    // Delete the item
    let delete_response = client.delete(&format!("{}/api/items/{}", BASE_URL, item_id))
        .send()
        .await
        .expect("Failed to delete item");

    assert_eq!(delete_response.status(), 204);

    // Verify item is deleted
    let get_deleted_response = client.get(&format!("{}/api/items/{}", BASE_URL, item_id))
        .send()
        .await
        .expect("Failed to get deleted item");

    assert_eq!(get_deleted_response.status(), 404);
}

#[tokio::test]
async fn test_cors_headers() {
    let client = reqwest::Client::new();
    let response = client.get(&format!("{}/api/status", BASE_URL))
        .send()
        .await
        .expect("Failed to send request");

    assert!(response.headers().contains_key("access-control-allow-origin"));
}

#[tokio::test]
async fn test_security_headers() {
    let client = reqwest::Client::new();
    let response = client.get(&format!("{}/", BASE_URL))
        .send()
        .await
        .expect("Failed to send request");

    let headers = response.headers();
    assert!(headers.contains_key("x-content-type-options"));
    assert!(headers.contains_key("x-frame-options"));
    assert!(headers.contains_key("x-xss-protection"));
    assert!(headers.contains_key("server"));
}

#[tokio::test]
async fn test_404_handling() {
    let client = reqwest::Client::new();
    let response = client.get(&format!("{}/nonexistent-file.txt", BASE_URL))
        .send()
        .await
        .expect("Failed to send request");

    assert_eq!(response.status(), 404);
}

#[tokio::test]
async fn test_invalid_json() {
    let client = reqwest::Client::new();
    let response = client.post(&format!("{}/api/items", BASE_URL))
        .header("content-type", "application/json")
        .body("invalid json")
        .send()
        .await
        .expect("Failed to send request");

    assert_eq!(response.status(), 400);
}
