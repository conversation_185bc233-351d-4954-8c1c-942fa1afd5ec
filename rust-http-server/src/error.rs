use hyper::{Response, StatusCode};
use http_body_util::Full;
use hyper::body::Bytes;
use serde_json::json;
use std::fmt;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ServerError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("HTTP error: {0}")]
    Http(#[from] hyper::Error),

    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("Configuration error: {0}")]
    Config(#[from] config::ConfigError),

    #[error("TOML parsing error: {0}")]
    Toml(#[from] toml::de::Error),

    #[error("File not found: {0}")]
    FileNotFound(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("Bad request: {0}")]
    BadRequest(String),

    #[error("Unauthorized: {0}")]
    Unauthorized(String),

    #[error("Forbidden: {0}")]
    Forbidden(String),

    #[error("Method not allowed: {0}")]
    MethodNotAllowed(String),

    #[error("Conflict: {0}")]
    Conflict(String),

    #[error("Unprocessable entity: {0}")]
    UnprocessableEntity(String),

    #[error("Internal server error: {0}")]
    Internal(String),

    #[error("Rate limit exceeded")]
    RateLimitExceeded,

    #[error("Request timeout")]
    Timeout,

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),
}

impl ServerError {
    pub fn status_code(&self) -> StatusCode {
        match self {
            ServerError::FileNotFound(_) => StatusCode::NOT_FOUND,
            ServerError::PermissionDenied(_) | ServerError::Forbidden(_) => StatusCode::FORBIDDEN,
            ServerError::BadRequest(_) => StatusCode::BAD_REQUEST,
            ServerError::Unauthorized(_) => StatusCode::UNAUTHORIZED,
            ServerError::MethodNotAllowed(_) => StatusCode::METHOD_NOT_ALLOWED,
            ServerError::Conflict(_) => StatusCode::CONFLICT,
            ServerError::UnprocessableEntity(_) => StatusCode::UNPROCESSABLE_ENTITY,
            ServerError::RateLimitExceeded => StatusCode::TOO_MANY_REQUESTS,
            ServerError::Timeout => StatusCode::REQUEST_TIMEOUT,
            ServerError::ServiceUnavailable(_) => StatusCode::SERVICE_UNAVAILABLE,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }

    pub fn to_response(&self) -> Response<Full<Bytes>> {
        let status = self.status_code();
        let error_response = json!({
            "error": {
                "code": status.as_u16(),
                "message": self.to_string(),
                "timestamp": chrono::Utc::now().to_rfc3339()
            }
        });

        Response::builder()
            .status(status)
            .header("content-type", "application/json")
            .body(Full::new(Bytes::from(error_response.to_string())))
            .unwrap_or_else(|_| {
                Response::builder()
                    .status(StatusCode::INTERNAL_SERVER_ERROR)
                    .body(Full::new(Bytes::from("Internal Server Error")))
                    .unwrap()
            })
    }
}

pub type Result<T> = std::result::Result<T, ServerError>;
