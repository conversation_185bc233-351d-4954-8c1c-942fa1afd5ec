use std::sync::Arc;
use std::time::Duration;

use hyper::server::conn::http1;
use hyper::service::service_fn;
use hyper::{Request, Response};
use hyper_util::rt::TokioIo;
use tokio::net::TcpListener;
use tracing::{info, error, warn};
use http_body_util::Full;
use hyper::body::Bytes;

use crate::config::Config;
use crate::error::{ServerError, Result};
use crate::handlers::RequestHandler;
use crate::middleware::MiddlewareStack;

pub struct HttpServer {
    config: Arc<Config>,
    handler: Arc<RequestHandler>,
    middleware: Arc<MiddlewareStack>,
}

impl HttpServer {
    pub async fn new(config: Config) -> Result<Self> {
        let config = Arc::new(config);
        let handler = Arc::new(RequestHandler::new(config.clone()).await?);
        let middleware = Arc::new(MiddlewareStack::new(config.clone()));

        Ok(Self {
            config,
            handler,
            middleware,
        })
    }

    pub async fn run(&self) -> Result<()> {
        let addr = self.config.bind_address();
        let listener = TcpListener::bind(&addr).await
            .map_err(|e| ServerError::Io(e))?;

        info!("Server listening on http://{}", addr);
        info!("Serving static files from: {:?}", self.config.static_dir);

        loop {
            let (stream, remote_addr) = match listener.accept().await {
                Ok(conn) => conn,
                Err(e) => {
                    error!("Failed to accept connection: {}", e);
                    continue;
                }
            };

            let io = TokioIo::new(stream);
            let handler = self.handler.clone();
            let middleware = self.middleware.clone();
            let config = self.config.clone();

            tokio::task::spawn(async move {
                let service = service_fn(move |req| {
                    let handler = handler.clone();
                    let middleware = middleware.clone();
                    async move {
                        // Apply middleware and handle request
                        match middleware.process_request(req, &handler).await {
                            Ok(response) => Ok::<_, hyper::Error>(response),
                            Err(e) => {
                                error!("Request handling error: {}", e);
                                Ok(e.to_response())
                            }
                        }
                    }
                });

                let conn = http1::Builder::new()
                    .serve_connection(io, service)
                    .with_upgrades();

                // Set connection timeout
                let timeout_duration = Duration::from_secs(config.request_timeout_secs);
                
                if let Err(err) = tokio::time::timeout(timeout_duration, conn).await {
                    warn!("Connection from {} timed out", remote_addr);
                } else if let Err(err) = conn.await {
                    error!("Connection error from {}: {}", remote_addr, err);
                }
            });
        }
    }
}
