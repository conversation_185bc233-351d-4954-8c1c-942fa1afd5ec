use std::path::Path<PERSON>uf;
use serde::{Deserialize, Serialize};
use crate::Args;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub host: String,
    pub port: u16,
    pub static_dir: PathBuf,
    pub verbose: bool,
    pub max_connections: usize,
    pub request_timeout_secs: u64,
    pub enable_cors: bool,
    pub cors_origins: Vec<String>,
    pub rate_limit_requests_per_minute: Option<u32>,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 8080,
            static_dir: PathBuf::from("./www"),
            verbose: false,
            max_connections: 1000,
            request_timeout_secs: 30,
            enable_cors: true,
            cors_origins: vec!["*".to_string()],
            rate_limit_requests_per_minute: Some(60),
        }
    }
}

impl Config {
    pub fn new(args: Args) -> Result<Self, Box<dyn std::error::Error>> {
        let mut config = if let Some(config_path) = args.config {
            // Load from config file
            let config_str = std::fs::read_to_string(config_path)?;
            toml::from_str::<Config>(&config_str)?
        } else {
            Config::default()
        };

        // Override with command line arguments
        config.host = args.host;
        config.port = args.port;
        config.static_dir = args.static_dir;
        config.verbose = args.verbose;

        // Validate configuration
        config.validate()?;

        Ok(config)
    }

    fn validate(&self) -> Result<(), Box<dyn std::error::Error>> {
        if self.port == 0 {
            return Err("Port cannot be 0".into());
        }

        if !self.static_dir.exists() {
            std::fs::create_dir_all(&self.static_dir)?;
        }

        Ok(())
    }

    pub fn bind_address(&self) -> String {
        format!("{}:{}", self.host, self.port)
    }
}
