use std::sync::Arc;
use std::path::{Path, PathBuf};

use hyper::{Request, Response, StatusCode, HeaderMap};
use http_body_util::Full;
use hyper::body::Bytes;
use tokio::fs;
use tracing::{debug, warn};
use percent_encoding::percent_decode_str;

use crate::config::Config;
use crate::error::{ServerError, Result};

pub struct StaticFileHandler {
    config: Arc<Config>,
}

impl StaticFileHandler {
    pub fn new(config: Arc<Config>) -> Self {
        Self { config }
    }

    pub async fn handle(&self, req: Request<hyper::body::Incoming>) -> Result<Response<Full<Bytes>>> {
        let path = req.uri().path();
        let decoded_path = percent_decode_str(path).decode_utf8()
            .map_err(|_| ServerError::BadRequest("Invalid URL encoding".to_string()))?;

        // Security: prevent directory traversal
        let safe_path = self.sanitize_path(&decoded_path)?;
        let file_path = self.config.static_dir.join(safe_path);

        debug!("Serving static file: {:?}", file_path);

        // Check if path exists and is within static directory
        if !file_path.starts_with(&self.config.static_dir) {
            return Err(ServerError::PermissionDenied("Path traversal not allowed".to_string()));
        }

        // Handle directory requests
        if file_path.is_dir() {
            return self.serve_directory(&file_path).await;
        }

        // Serve file
        self.serve_file(&file_path).await
    }

    fn sanitize_path(&self, path: &str) -> Result<PathBuf> {
        let path = path.trim_start_matches('/');
        
        // Default to index.html for root
        let path = if path.is_empty() { "index.html" } else { path };
        
        // Remove any path traversal attempts
        let components: Vec<&str> = path.split('/')
            .filter(|&component| !component.is_empty() && component != "." && component != "..")
            .collect();
        
        Ok(components.iter().collect())
    }

    async fn serve_file(&self, file_path: &Path) -> Result<Response<Full<Bytes>>> {
        match fs::read(file_path).await {
            Ok(contents) => {
                let mime_type = mime_guess::from_path(file_path)
                    .first_or_octet_stream()
                    .to_string();

                let mut response = Response::builder()
                    .status(StatusCode::OK)
                    .header("content-type", mime_type);

                // Add caching headers
                response = response
                    .header("cache-control", "public, max-age=3600")
                    .header("etag", format!("\"{}\"", self.calculate_etag(&contents)));

                Ok(response
                    .body(Full::new(Bytes::from(contents)))
                    .unwrap())
            }
            Err(e) => match e.kind() {
                std::io::ErrorKind::NotFound => {
                    Err(ServerError::FileNotFound(file_path.display().to_string()))
                }
                std::io::ErrorKind::PermissionDenied => {
                    Err(ServerError::PermissionDenied(file_path.display().to_string()))
                }
                _ => Err(ServerError::Io(e)),
            }
        }
    }

    async fn serve_directory(&self, dir_path: &Path) -> Result<Response<Full<Bytes>>> {
        // Try to serve index.html from the directory
        let index_path = dir_path.join("index.html");
        if index_path.exists() {
            return self.serve_file(&index_path).await;
        }

        // Generate directory listing
        self.generate_directory_listing(dir_path).await
    }

    async fn generate_directory_listing(&self, dir_path: &Path) -> Result<Response<Full<Bytes>>> {
        let mut entries = fs::read_dir(dir_path).await
            .map_err(ServerError::Io)?;

        let mut html = String::from(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>Directory Listing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .file { margin: 5px 0; }
        .dir { font-weight: bold; }
        a { text-decoration: none; color: #0066cc; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>Directory Listing</h1>
    <div class="file"><a href="../">../</a></div>
"#,
        );

        while let Some(entry) = entries.next_entry().await.map_err(ServerError::Io)? {
            let name = entry.file_name().to_string_lossy().to_string();
            let is_dir = entry.file_type().await.map_err(ServerError::Io)?.is_dir();
            
            let class = if is_dir { "dir" } else { "file" };
            let display_name = if is_dir { format!("{}/", name) } else { name.clone() };
            
            html.push_str(&format!(
                r#"    <div class="{}"><a href="{}">{}</a></div>"#,
                class, name, display_name
            ));
        }

        html.push_str("</body></html>");

        Ok(Response::builder()
            .status(StatusCode::OK)
            .header("content-type", "text/html; charset=utf-8")
            .body(Full::new(Bytes::from(html)))
            .unwrap())
    }

    fn calculate_etag(&self, contents: &[u8]) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        contents.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}
