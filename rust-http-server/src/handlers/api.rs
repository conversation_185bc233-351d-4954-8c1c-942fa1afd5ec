use std::sync::Arc;
use std::collections::HashMap;

use hyper::{Request, Response, Method, StatusCode};
use http_body_util::{Full, BodyExt};
use hyper::body::Bytes;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use tokio::sync::RwLock;
use uuid::Uuid;
use tracing::{debug, info};

use crate::config::Config;
use crate::error::{ServerError, Result};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Item {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Deserialize)]
pub struct CreateItemRequest {
    pub name: String,
    pub description: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateItemRequest {
    pub name: Option<String>,
    pub description: Option<String>,
}

pub struct ApiHandler {
    config: Arc<Config>,
    // Simple in-memory storage for demo purposes
    items: Arc<RwLock<HashMap<String, Item>>>,
}

impl ApiHandler {
    pub fn new(config: Arc<Config>) -> Self {
        Self {
            config,
            items: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn handle(&self, req: Request<hyper::body::Incoming>) -> Result<Response<Full<Bytes>>> {
        let method = req.method();
        let path = req.uri().path();
        
        debug!("API request: {} {}", method, path);

        match (method, path) {
            (&Method::GET, "/api/items") => self.list_items().await,
            (&Method::POST, "/api/items") => self.create_item(req).await,
            (&Method::GET, path) if path.starts_with("/api/items/") => {
                let id = path.strip_prefix("/api/items/").unwrap();
                self.get_item(id).await
            }
            (&Method::PUT, path) if path.starts_with("/api/items/") => {
                let id = path.strip_prefix("/api/items/").unwrap();
                self.update_item(id, req).await
            }
            (&Method::DELETE, path) if path.starts_with("/api/items/") => {
                let id = path.strip_prefix("/api/items/").unwrap();
                self.delete_item(id).await
            }
            (&Method::GET, "/api/status") => self.get_status().await,
            _ => Err(ServerError::BadRequest("API endpoint not found".to_string())),
        }
    }

    async fn list_items(&self) -> Result<Response<Full<Bytes>>> {
        let items = self.items.read().await;
        let items_vec: Vec<&Item> = items.values().collect();
        
        let response = serde_json::json!({
            "items": items_vec,
            "count": items_vec.len()
        });

        Ok(self.json_response(StatusCode::OK, response))
    }

    async fn create_item(&self, req: Request<hyper::body::Incoming>) -> Result<Response<Full<Bytes>>> {
        let body = req.collect().await.map_err(|e| ServerError::Http(e))?.to_bytes();
        let create_req: CreateItemRequest = serde_json::from_slice(&body)
            .map_err(|e| ServerError::BadRequest(format!("Invalid JSON: {}", e)))?;

        let now = chrono::Utc::now();
        let item = Item {
            id: Uuid::new_v4().to_string(),
            name: create_req.name,
            description: create_req.description,
            created_at: now,
            updated_at: now,
        };

        let mut items = self.items.write().await;
        items.insert(item.id.clone(), item.clone());
        
        info!("Created item: {}", item.id);
        Ok(self.json_response(StatusCode::CREATED, item))
    }

    async fn get_item(&self, id: &str) -> Result<Response<Full<Bytes>>> {
        let items = self.items.read().await;
        
        match items.get(id) {
            Some(item) => Ok(self.json_response(StatusCode::OK, item)),
            None => Err(ServerError::FileNotFound(format!("Item with id {} not found", id))),
        }
    }

    async fn update_item(&self, id: &str, req: Request<hyper::body::Incoming>) -> Result<Response<Full<Bytes>>> {
        let body = req.collect().await.map_err(|e| ServerError::Http(e))?.to_bytes();
        let update_req: UpdateItemRequest = serde_json::from_slice(&body)
            .map_err(|e| ServerError::BadRequest(format!("Invalid JSON: {}", e)))?;

        let mut items = self.items.write().await;
        
        match items.get_mut(id) {
            Some(item) => {
                if let Some(name) = update_req.name {
                    item.name = name;
                }
                if let Some(description) = update_req.description {
                    item.description = Some(description);
                }
                item.updated_at = chrono::Utc::now();
                
                info!("Updated item: {}", id);
                Ok(self.json_response(StatusCode::OK, item.clone()))
            }
            None => Err(ServerError::FileNotFound(format!("Item with id {} not found", id))),
        }
    }

    async fn delete_item(&self, id: &str) -> Result<Response<Full<Bytes>>> {
        let mut items = self.items.write().await;
        
        match items.remove(id) {
            Some(_) => {
                info!("Deleted item: {}", id);
                Ok(self.json_response(StatusCode::NO_CONTENT, serde_json::json!({})))
            }
            None => Err(ServerError::FileNotFound(format!("Item with id {} not found", id))),
        }
    }

    async fn get_status(&self) -> Result<Response<Full<Bytes>>> {
        let items = self.items.read().await;
        let status = serde_json::json!({
            "status": "ok",
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "items_count": items.len(),
            "version": env!("CARGO_PKG_VERSION")
        });

        Ok(self.json_response(StatusCode::OK, status))
    }

    fn json_response<T: Serialize>(&self, status: StatusCode, data: T) -> Response<Full<Bytes>> {
        let json = serde_json::to_string(&data).unwrap_or_else(|_| "{}".to_string());
        
        Response::builder()
            .status(status)
            .header("content-type", "application/json")
            .body(Full::new(Bytes::from(json)))
            .unwrap()
    }
}
