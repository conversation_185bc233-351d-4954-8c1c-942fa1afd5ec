use std::sync::Arc;
use std::path::PathBuf;

use hyper::{Request, Response, Method, StatusCode};
use http_body_util::Full;
use hyper::body::Bytes;
use tracing::{info, debug};

use crate::config::Config;
use crate::error::{ServerError, Result};

pub mod static_files;
pub mod api;

use static_files::StaticFileHandler;
use api::ApiHandler;

pub struct RequestHandler {
    config: Arc<Config>,
    static_handler: StaticFileHandler,
    api_handler: ApiHandler,
}

impl RequestHandler {
    pub async fn new(config: Arc<Config>) -> Result<Self> {
        let static_handler = StaticFileHandler::new(config.clone());
        let api_handler = ApiHandler::new(config.clone());

        Ok(Self {
            config,
            static_handler,
            api_handler,
        })
    }

    pub async fn handle_request(&self, req: Request<hyper::body::Incoming>) -> Result<Response<Full<Bytes>>> {
        let method = req.method();
        let path = req.uri().path();
        
        debug!("Handling {} {}", method, path);

        match (method, path) {
            // API routes
            (_, path) if path.starts_with("/api/") => {
                self.api_handler.handle(req).await
            }
            
            // Health check
            (&Method::GET, "/health") => {
                self.health_check().await
            }
            
            // Static files (default)
            (&Method::GET, _) => {
                self.static_handler.handle(req).await
            }
            
            // Method not allowed for non-GET requests to static files
            _ => {
                Err(ServerError::BadRequest("Method not allowed".to_string()))
            }
        }
    }

    async fn health_check(&self) -> Result<Response<Full<Bytes>>> {
        let health_info = serde_json::json!({
            "status": "healthy",
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "version": env!("CARGO_PKG_VERSION"),
            "uptime": "TODO: implement uptime tracking"
        });

        Ok(Response::builder()
            .status(StatusCode::OK)
            .header("content-type", "application/json")
            .body(Full::new(Bytes::from(health_info.to_string())))
            .unwrap())
    }
}
