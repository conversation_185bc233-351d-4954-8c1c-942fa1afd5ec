use std::sync::Arc;
use std::collections::HashMap;
use std::net::IpAddr;
use std::time::{Duration, Instant};

use hyper::Request;
use tokio::sync::RwLock;

use crate::config::Config;
use crate::error::{ServerError, Result};

#[derive(Debug, Clone)]
struct RateLimitEntry {
    count: u32,
    window_start: Instant,
}

pub struct RateLimitMiddleware {
    config: Arc<Config>,
    // Simple in-memory rate limiting (in production, use Redis or similar)
    rate_limits: Arc<RwLock<HashMap<IpAddr, RateLimitEntry>>>,
}

impl RateLimitMiddleware {
    pub fn new(config: Arc<Config>) -> Self {
        Self {
            config,
            rate_limits: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn check_rate_limit(&self, req: Request<hyper::body::Incoming>) -> Result<Request<hyper::body::Incoming>> {
        // Skip rate limiting if not configured
        let limit = match self.config.rate_limit_requests_per_minute {
            Some(limit) => limit,
            None => return Ok(req),
        };

        // Extract client IP (simplified - in production, handle X-Forwarded-For, etc.)
        let client_ip = self.extract_client_ip(&req);
        
        let mut rate_limits = self.rate_limits.write().await;
        let now = Instant::now();
        let window_duration = Duration::from_secs(60); // 1 minute window

        let entry = rate_limits.entry(client_ip).or_insert(RateLimitEntry {
            count: 0,
            window_start: now,
        });

        // Reset window if expired
        if now.duration_since(entry.window_start) >= window_duration {
            entry.count = 0;
            entry.window_start = now;
        }

        // Check if limit exceeded
        if entry.count >= limit {
            return Err(ServerError::RateLimitExceeded);
        }

        // Increment counter
        entry.count += 1;

        // Clean up old entries periodically (simple cleanup)
        if rate_limits.len() > 10000 {
            rate_limits.retain(|_, entry| {
                now.duration_since(entry.window_start) < window_duration
            });
        }

        Ok(req)
    }

    fn extract_client_ip(&self, req: &Request<hyper::body::Incoming>) -> IpAddr {
        // In a real implementation, you'd want to:
        // 1. Check X-Forwarded-For header
        // 2. Check X-Real-IP header
        // 3. Fall back to connection IP
        // For now, we'll use a placeholder
        "127.0.0.1".parse().unwrap()
    }
}
