use std::sync::Arc;
use std::time::Instant;

use hyper::{Request, Response, HeaderMap};
use http_body_util::Full;
use hyper::body::Bytes;
use tracing::{info, debug};

use crate::config::Config;
use crate::error::{ServerError, Result};
use crate::handlers::RequestHandler;

pub mod cors;
pub mod rate_limit;
pub mod logging;

use cors::CorsMiddleware;
use rate_limit::RateLimitMiddleware;
use logging::LoggingMiddleware;

pub struct MiddlewareStack {
    config: Arc<Config>,
    cors: CorsMiddleware,
    rate_limit: RateLimitMiddleware,
    logging: LoggingMiddleware,
}

impl MiddlewareStack {
    pub fn new(config: Arc<Config>) -> Self {
        Self {
            cors: CorsMiddleware::new(config.clone()),
            rate_limit: RateLimitMiddleware::new(config.clone()),
            logging: LoggingMiddleware::new(config.clone()),
            config,
        }
    }

    pub async fn process_request(
        &self,
        req: Request<hyper::body::Incoming>,
        handler: &RequestHandler,
    ) -> Result<Response<Full<Bytes>>> {
        let start_time = Instant::now();
        let method = req.method().clone();
        let path = req.uri().path().to_string();
        let user_agent = req.headers()
            .get("user-agent")
            .and_then(|v| v.to_str().ok())
            .unwrap_or("unknown")
            .to_string();

        // Apply pre-request middleware
        let req = self.logging.pre_request(req).await?;
        let req = self.rate_limit.check_rate_limit(req).await?;

        // Handle the request
        let mut response = handler.handle_request(req).await?;

        // Apply post-response middleware
        response = self.cors.add_cors_headers(response).await?;
        response = self.add_security_headers(response).await?;

        // Log the response
        let duration = start_time.elapsed();
        self.logging.log_response(&method, &path, response.status(), duration, &user_agent).await;

        Ok(response)
    }

    async fn add_security_headers(&self, mut response: Response<Full<Bytes>>) -> Result<Response<Full<Bytes>>> {
        let headers = response.headers_mut();
        
        // Security headers
        headers.insert("x-content-type-options", "nosniff".parse().unwrap());
        headers.insert("x-frame-options", "DENY".parse().unwrap());
        headers.insert("x-xss-protection", "1; mode=block".parse().unwrap());
        headers.insert("referrer-policy", "strict-origin-when-cross-origin".parse().unwrap());
        
        // Server identification
        headers.insert("server", format!("rust-http-server/{}", env!("CARGO_PKG_VERSION")).parse().unwrap());

        Ok(response)
    }
}
