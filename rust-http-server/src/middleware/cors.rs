use std::sync::Arc;

use hyper::{Response, Method, HeaderMap};
use http_body_util::Full;
use hyper::body::Bytes;

use crate::config::Config;
use crate::error::Result;

pub struct CorsMiddleware {
    config: Arc<Config>,
}

impl CorsMiddleware {
    pub fn new(config: Arc<Config>) -> Self {
        Self { config }
    }

    pub async fn add_cors_headers(&self, mut response: Response<Full<Bytes>>) -> Result<Response<Full<Bytes>>> {
        if !self.config.enable_cors {
            return Ok(response);
        }

        let headers = response.headers_mut();

        // Add CORS headers
        let origins = self.config.cors_origins.join(", ");
        headers.insert("access-control-allow-origin", origins.parse().unwrap());
        headers.insert("access-control-allow-methods", "GET, POST, PUT, DELETE, OPTIONS".parse().unwrap());
        headers.insert("access-control-allow-headers", "content-type, authorization, x-requested-with".parse().unwrap());
        headers.insert("access-control-max-age", "86400".parse().unwrap()); // 24 hours

        Ok(response)
    }

    pub fn handle_preflight(&self) -> Result<Response<Full<Bytes>>> {
        if !self.config.enable_cors {
            return Ok(Response::builder()
                .status(405)
                .body(Full::new(Bytes::from("Method Not Allowed")))
                .unwrap());
        }

        let origins = self.config.cors_origins.join(", ");
        
        Ok(Response::builder()
            .status(200)
            .header("access-control-allow-origin", origins)
            .header("access-control-allow-methods", "GET, POST, PUT, DELETE, OPTIONS")
            .header("access-control-allow-headers", "content-type, authorization, x-requested-with")
            .header("access-control-max-age", "86400")
            .header("content-length", "0")
            .body(Full::new(Bytes::new()))
            .unwrap())
    }
}
