use std::sync::Arc;
use std::time::Duration;

use hyper::{Request, Method, StatusCode};
use tracing::{info, debug, warn};

use crate::config::Config;
use crate::error::Result;

pub struct LoggingMiddleware {
    config: Arc<Config>,
}

impl LoggingMiddleware {
    pub fn new(config: Arc<Config>) -> Self {
        Self { config }
    }

    pub async fn pre_request(&self, req: Request<hyper::body::Incoming>) -> Result<Request<hyper::body::Incoming>> {
        let method = req.method();
        let path = req.uri().path();
        let query = req.uri().query().unwrap_or("");
        
        debug!("Incoming request: {} {} {}", method, path, 
               if query.is_empty() { "" } else { &format!("?{}", query) });

        if self.config.verbose {
            // Log headers in verbose mode
            for (name, value) in req.headers() {
                debug!("Header: {}: {:?}", name, value);
            }
        }

        Ok(req)
    }

    pub async fn log_response(
        &self,
        method: &Method,
        path: &str,
        status: StatusCode,
        duration: Duration,
        user_agent: &str,
    ) {
        let duration_ms = duration.as_millis();
        
        let log_message = format!(
            "{} {} {} {}ms \"{}\"",
            method,
            path,
            status.as_u16(),
            duration_ms,
            user_agent
        );

        match status.as_u16() {
            200..=299 => info!("{}", log_message),
            300..=399 => info!("{}", log_message),
            400..=499 => warn!("{}", log_message),
            500..=599 => warn!("{}", log_message),
            _ => info!("{}", log_message),
        }

        // Log slow requests
        if duration_ms > 1000 {
            warn!("Slow request detected: {} took {}ms", path, duration_ms);
        }
    }
}
