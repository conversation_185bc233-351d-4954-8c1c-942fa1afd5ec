// Library exports for testing
pub mod config;
pub mod error;
pub mod handlers;
pub mod middleware;
pub mod server;

pub use config::Config;
pub use error::{ServerError, Result};
pub use server::HttpServer;

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_config_default() {
        let config = Config::default();
        assert_eq!(config.host, "127.0.0.1");
        assert_eq!(config.port, 8080);
        assert_eq!(config.static_dir, PathBuf::from("./www"));
        assert!(!config.verbose);
        assert!(config.enable_cors);
    }

    #[test]
    fn test_config_bind_address() {
        let config = Config {
            host: "0.0.0.0".to_string(),
            port: 3000,
            ..Config::default()
        };
        assert_eq!(config.bind_address(), "0.0.0.0:3000");
    }

    #[test]
    fn test_server_error_status_codes() {
        assert_eq!(ServerError::FileNotFound("test".to_string()).status_code(), hyper::StatusCode::NOT_FOUND);
        assert_eq!(ServerError::BadRequest("test".to_string()).status_code(), hyper::StatusCode::BAD_REQUEST);
        assert_eq!(ServerError::Unauthorized("test".to_string()).status_code(), hyper::StatusCode::UNAUTHORIZED);
        assert_eq!(ServerError::RateLimitExceeded.status_code(), hyper::StatusCode::TOO_MANY_REQUESTS);
    }

    #[test]
    fn test_error_to_response() {
        let error = ServerError::BadRequest("Invalid input".to_string());
        let response = error.to_response();
        assert_eq!(response.status(), hyper::StatusCode::BAD_REQUEST);
    }
}
