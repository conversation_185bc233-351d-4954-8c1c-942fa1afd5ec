use std::convert::Infallible;
use std::net::SocketAddr;
use std::path::PathBuf;
use std::sync::Arc;

use clap::Parser;
use hyper::server::conn::http1;
use hyper::service::service_fn;
use hyper::{Request, Response, StatusCode};
use hyper_util::rt::TokioIo;
use tokio::net::TcpListener;
use tracing::{info, warn, error};

mod config;
mod error;
mod handlers;
mod middleware;
mod server;

use config::Config;
use error::ServerError;
use server::HttpServer;

#[derive(Parser, Debug)]
#[command(name = "rust-http-server")]
#[command(about = "A high-performance HTTP server written in Rust")]
struct Args {
    /// Port to bind to
    #[arg(short, long, default_value = "8080")]
    port: u16,

    /// Host to bind to
    #[arg(long, default_value = "127.0.0.1")]
    host: String,

    /// Directory to serve static files from
    #[arg(short, long, default_value = "./www")]
    static_dir: PathBuf,

    /// Enable verbose logging
    #[arg(short, long)]
    verbose: bool,

    /// Configuration file path
    #[arg(short, long)]
    config: Option<PathBuf>,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args = Args::parse();

    // Initialize tracing
    let log_level = if args.verbose { "debug" } else { "info" };
    tracing_subscriber::fmt()
        .with_env_filter(format!("rust_http_server={}", log_level))
        .init();

    // Load configuration
    let config = Config::new(args)?;
    info!("Starting server with config: {:?}", config);

    // Create and start the server
    let server = HttpServer::new(config).await?;
    server.run().await?;

    Ok(())
}
