# 🦀 Rust HTTP Server

A high-performance, production-ready HTTP server written in Rust using Tokio and Hyper.

## ✨ Features

- **🚀 High Performance**: Built with Tokio async runtime and Hyper for maximum throughput
- **📁 Static File Serving**: Efficient static file serving with MIME type detection and caching
- **🔌 RESTful API**: JSON API endpoints with full CRUD operations
- **🛡️ Security**: CORS support, rate limiting, security headers, and input validation
- **📊 Middleware**: Comprehensive logging, error handling, and request processing
- **⚙️ Configurable**: CLI arguments, environment variables, and config file support
- **🧪 Well Tested**: Unit tests, integration tests, and benchmarks included

## 🚀 Quick Start

### Prerequisites

Make sure you have Rust installed. If not, install it from [rustup.rs](https://rustup.rs/).

### Installation & Running

1. **Clone and build:**
   ```bash
   cd rust-http-server
   cargo build --release
   ```

2. **Run the server:**
   ```bash
   cargo run --release
   ```

3. **Visit the server:**
   Open your browser to `http://localhost:8080`

## 📖 Usage

### Command Line Options

```bash
cargo run -- --help
```

```
A high-performance HTTP server written in Rust

Usage: rust-http-server [OPTIONS]

Options:
  -p, --port <PORT>              Port to bind to [default: 8080]
      --host <HOST>              Host to bind to [default: 127.0.0.1]
  -s, --static-dir <STATIC_DIR>  Directory to serve static files from [default: ./www]
  -v, --verbose                  Enable verbose logging
  -c, --config <CONFIG>          Configuration file path
  -h, --help                     Print help
```

### Examples

```bash
# Run on port 3000
cargo run -- --port 3000

# Run with verbose logging
cargo run -- --verbose

# Use custom static directory
cargo run -- --static-dir /path/to/files

# Use configuration file
cargo run -- --config config.toml

# Bind to all interfaces
cargo run -- --host 0.0.0.0 --port 8080
```

### Configuration File

Create a `config.toml` file:

```toml
# Server settings
host = "127.0.0.1"
port = 8080
static_dir = "./www"
verbose = false

# Performance settings
max_connections = 1000
request_timeout_secs = 30

# CORS settings
enable_cors = true
cors_origins = ["*"]

# Rate limiting (requests per minute per IP)
rate_limit_requests_per_minute = 60
```

## 🔌 API Endpoints

### Health & Status

- `GET /health` - Health check endpoint
- `GET /api/status` - API status and statistics

### Items CRUD API

- `GET /api/items` - List all items
- `POST /api/items` - Create a new item
- `GET /api/items/{id}` - Get a specific item
- `PUT /api/items/{id}` - Update an item
- `DELETE /api/items/{id}` - Delete an item

### API Examples

**Create an item:**
```bash
curl -X POST http://localhost:8080/api/items \
  -H "Content-Type: application/json" \
  -d '{"name": "My Item", "description": "A test item"}'
```

**List items:**
```bash
curl http://localhost:8080/api/items
```

**Update an item:**
```bash
curl -X PUT http://localhost:8080/api/items/{id} \
  -H "Content-Type: application/json" \
  -d '{"name": "Updated Item"}'
```

## 🧪 Testing

### Run Unit Tests
```bash
cargo test
```

### Run Integration Tests
```bash
# Start the server first
cargo run &

# Run integration tests
cargo test --test integration_tests

# Stop the server
pkill rust-http-server
```

### Performance Testing
```bash
# Install wrk for load testing
# Ubuntu/Debian: sudo apt install wrk
# macOS: brew install wrk

# Test static file serving
wrk -t12 -c400 -d30s http://localhost:8080/

# Test API endpoints
wrk -t12 -c400 -d30s http://localhost:8080/api/status
```

## 🏗️ Architecture

```
src/
├── main.rs              # Application entry point
├── lib.rs               # Library exports
├── config.rs            # Configuration management
├── error.rs             # Error types and handling
├── server.rs            # Main server implementation
├── handlers/            # Request handlers
│   ├── mod.rs          # Handler coordination
│   ├── static_files.rs # Static file serving
│   └── api.rs          # API endpoints
└── middleware/          # Middleware stack
    ├── mod.rs          # Middleware coordination
    ├── cors.rs         # CORS handling
    ├── rate_limit.rs   # Rate limiting
    └── logging.rs      # Request logging
```

## 🛡️ Security Features

- **CORS Protection**: Configurable cross-origin resource sharing
- **Rate Limiting**: Per-IP request rate limiting
- **Security Headers**: X-Content-Type-Options, X-Frame-Options, etc.
- **Input Validation**: JSON schema validation and sanitization
- **Path Traversal Protection**: Prevents directory traversal attacks
- **Error Information Disclosure**: Sanitized error responses

## 🚀 Performance

- **Async/Await**: Full async implementation using Tokio
- **Zero-Copy**: Efficient memory usage with minimal allocations
- **Connection Pooling**: Efficient connection management
- **Caching Headers**: Proper HTTP caching for static files
- **Optimized Builds**: Release builds with LTO and optimizations

## 📊 Monitoring

The server provides comprehensive logging:

- Request/response logging with timing
- Error tracking and reporting
- Performance metrics
- Health check endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 🐳 Docker Support

Build and run with Docker:

```bash
# Build the image
docker build -t rust-http-server .

# Run the container
docker run -p 8080:8080 rust-http-server

# Run with custom configuration
docker run -p 8080:8080 -v $(pwd)/config.toml:/app/config.toml rust-http-server --config config.toml
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [Tokio](https://tokio.rs/) for async runtime
- HTTP handling by [Hyper](https://hyper.rs/)
- CLI parsing with [Clap](https://clap.rs/)
- Logging with [Tracing](https://tracing.rs/)
