#!/bin/bash

# Build script for Rust HTTP Server

set -e

echo "🦀 Building Rust HTTP Server..."

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Rust/Cargo not found. Please install Rust from https://rustup.rs/"
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
cargo clean

# Run tests
echo "🧪 Running tests..."
cargo test

# Build in release mode
echo "🔨 Building in release mode..."
cargo build --release

# Create www directory if it doesn't exist
if [ ! -d "www" ]; then
    echo "📁 Creating www directory..."
    mkdir -p www
fi

# Copy sample files if they don't exist
if [ ! -f "www/index.html" ]; then
    echo "📄 Sample index.html already exists"
fi

echo "✅ Build completed successfully!"
echo ""
echo "🚀 To run the server:"
echo "   ./target/release/rust-http-server"
echo ""
echo "🌐 Or with cargo:"
echo "   cargo run --release"
echo ""
echo "📖 For more options:"
echo "   ./target/release/rust-http-server --help"
